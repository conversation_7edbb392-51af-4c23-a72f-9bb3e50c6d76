import React, { useEffect, useRef, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Message } from '@/types';
import MessageBubble from './MessageBubble';
import TypingIndicator from './TypingIndicator';
import SuggestionChips from './SuggestionChips';

interface ChatMessagesProps {
  messages: Message[];
  isLoading: boolean;
  onSuggestionClick: (query: string) => void;
  showWelcome?: boolean;
  bottomRef?: React.RefObject<HTMLDivElement>; // NEW
}

const ChatMessages: React.FC<ChatMessagesProps> = ({
  messages,
  isLoading,
  onSuggestionClick,
  showWelcome = true,
  bottomRef // NEW
}) => {
  // const messagesEndRef = useRef<HTMLDivElement>(null); // REMOVE
  const containerRef = useRef<HTMLDivElement>(null);
  const [isUserScrolling, setIsUserScrolling] = useState(false);

  useEffect(() => {
    // Auto-scroll to bottom when new messages arrive
    if (bottomRef?.current) {
      bottomRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, isLoading, bottomRef]);

  // Group consecutive messages from the same sender
  const groupedMessages = React.useMemo(() => {
    const groups: { messages: Message[]; sender: string; timestamp: Date }[] = [];

    messages.forEach((message) => {
      const lastGroup = groups[groups.length - 1];
      const sender = message.isUser || message.sender === 'user' ? 'user' : 'assistant';

      if (lastGroup && lastGroup.sender === sender) {
        lastGroup.messages.push(message);
      } else {
        groups.push({
          messages: [message],
          sender,
          timestamp: new Date(message.timestamp)
        });
      }
    });

    return groups;
  }, [messages]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const welcomeVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  return (
    <div className="relative flex-1 overflow-hidden">
      <div
        ref={containerRef}
        className="h-full overflow-y-auto scroll-smooth"
      >
        <div className="max-w-4xl mx-auto px-4 py-6">
          {/* Welcome Message */}
          {false && messages.length === 0 && showWelcome && (
            <motion.div
              variants={welcomeVariants}
              initial="hidden"
              animate="visible"
              className="text-center py-12 bg-background text-foreground"
            >
              <div className="mb-8">
                <h1 className="text-4xl font-bold mb-4 text-gray-900 dark:text-white">
                  👋 Welcome to <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">Proxima28</span>
                </h1>
                <p className="text-lg max-w-2xl mx-auto text-gray-600 dark:text-gray-300">
                  I can help you with questions about company policies, employee guidelines, and HR procedures.
                </p>
              </div>

              {onSuggestionClick && (
                <div className="mt-8">
                  <p className="text-sm text-muted-foreground mb-4">Try asking about:</p>
                  <SuggestionChips
                    onSuggestionClick={onSuggestionClick}
                    className="justify-center"
                  />
                </div>
              )}
            </motion.div>
          )}

          {/* Messages */}
          <AnimatePresence mode="popLayout">
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              className="space-y-0.5"
            >
              {(() => {
                let lastUserMessage: Message | null = null;
                return groupedMessages.map((group, groupIndex) => (
                  <div key={groupIndex} className="space-y-1">
                    {group.messages.map((message, messageIndex) => {
                      let previousUserMessage: Message | null = lastUserMessage;
                      if (message.isUser || message.sender === 'user') {
                        lastUserMessage = message;
                        previousUserMessage = null;
                      } else if (lastUserMessage) {
                        previousUserMessage = lastUserMessage;
                      }
                      return (
                        <MessageBubble
                          key={message.id}
                          message={message}
                          isGrouped={messageIndex > 0}
                          showAvatar={messageIndex === 0}
                          previousUserMessage={previousUserMessage}
                        />
                      );
                    })}
                  </div>
                ));
              })()}
            </motion.div>
          </AnimatePresence>

          {/* Typing Indicator */}
          <AnimatePresence>
            {isLoading && (
              <TypingIndicator />
            )}
          </AnimatePresence>

          {/* Scroll anchor */}
          <div ref={bottomRef} className="h-4" />
        </div>
      </div>
    </div>
  );
};

export default ChatMessages;
