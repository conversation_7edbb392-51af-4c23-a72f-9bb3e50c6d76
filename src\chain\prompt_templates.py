"""
Universal HR Assistant Prompt Template for Proxima28 HR Chatbot
Compatible with Groq (LLaMA 3, Mistral), OpenAI (GPT-4, GPT-4o), <PERSON> (Anthropic), and <PERSON> (Google)
"""

from langchain_core.prompts import (
    ChatPromptTemplate,
    MessagesPlaceholder,
    SystemMessagePromptTemplate,
    HumanMessagePromptTemplate
)
from typing import List, Dict, Any
import json

from ..utils.logger import get_logger
logger = get_logger(__name__)

# Universal system guidelines applied to all response modes
UNIVERSAL_SYSTEM_GUIDELINES = """
- Use only provided document context
- Be exact: Use precise numbers, names, dates, and legal references
- Do not guess or use external knowledge
- If information unavailable, state: "This information is not available in the current documents"
- Prioritize documents matching query keywords
"""

# Token budget limits
TOKEN_LIMITS = {
    "concise": 2500,
    "detailed": 3500
}

# RAG configuration
RAG_CONFIGS = {
    "concise": {
        "max_chunks": 4,
        "top_chunks": 2,
        "token_limit": 2500
    },
    "detailed": {
        "max_chunks": 8,
        "top_chunks": 5,
        "token_limit": 3500
    }
}

# Response mode instructions
RESPONSE_MODE_INSTRUCTIONS = {
    "concise": "Return 2–3 factual bullet points. No examples or long explanations. End with 'Let me know if you need more details.'",
    "detailed": "Provide comprehensive explanation with clear headings and bullet points. Include examples and full context. End with helpful suggestion or offer to clarify.",
    "auto": "Respond accurately using context. Format clearly based on query complexity."
}


def create_hr_assistant_prompt(language: str = "English", response_mode: str = "auto", include_reasoning: bool = False) -> ChatPromptTemplate:
    """
    Creates a universal prompt template compatible with all major LLM providers.
    """
    
    mode_instructions = get_response_mode_instructions(response_mode)
    
    universal_prompt = f"""You are Proxima28, HR assistant at Ziantrix Technology Solutions. Always introduce yourself as Proxima28. Be friendly, professional, and clear.

Use the following document context to answer user queries accurately.

Context:
{{context}}

User Query:
{{query}}

Instructions:
{mode_instructions}

{UNIVERSAL_SYSTEM_GUIDELINES}

Response Language: {language}

{"Include reasoning before final answer." if include_reasoning else ""}"""

    return ChatPromptTemplate.from_template(universal_prompt)


def create_system_prompt(language: str, response_mode: str) -> str:
    """
    Creates system prompt with mode-specific RAG instructions.
    """
    
    rag_config = RAG_CONFIGS.get(response_mode, RAG_CONFIGS["detailed"])
    
    rag_instructions = f"""
## RAG Instructions:
- Retrieve up to {rag_config['max_chunks']} relevant chunks
- Use reranker to choose top {rag_config['top_chunks']} for context  
- Limit context to {rag_config['token_limit']} tokens
{UNIVERSAL_SYSTEM_GUIDELINES}
"""

    mode_instructions = get_response_mode_instructions(response_mode)
    
    return f"""You are Proxima28, AI HR Assistant for Ziantrix Technology Solutions. Always introduce yourself as Proxima28. Be friendly, professional, and consistent.

{rag_instructions}

## Supported Tasks:
- **Document summarization** (summarize resume/policy)
- **Text extraction** (extract sections from documents) 
- **Entity extraction** (identify names, dates, organizations)

## Task Instructions:

### Text Extraction:
Return exact text from document without paraphrasing. If asked for full document, return all available text.

### Entity Extraction:
Return list of named entities:
- **John Doe** (PERSON)
- **Ziantrix Technology Solutions** (ORG)
- **January 2023** (DATE)

### Summarization:
Use format: Purpose, Scope, Key Rules, Responsibilities, Consequences, Support, Legal References

## Formatting:
- Use **headings** and **bold** for important info
- Use bullet points for clarity
- End with clear next step

## Response Style: {mode_instructions}
## Language: {language}
"""


def get_response_mode_instructions(mode: str) -> str:
    """
    Returns instructions for specified response mode.
    """
    mode = mode.strip().lower()
    return RESPONSE_MODE_INSTRUCTIONS.get(mode, RESPONSE_MODE_INSTRUCTIONS["auto"])


def create_formatted_response_template(response_type: str) -> str:
    """
    Creates formatted response templates.
    """
    templates = {
        "leave_policy": """
## Leave Policy Summary

{policy_details}

**Available Leave Types:**
{leave_types}

**Need Help?** I can assist with eligibility and applications.
""",
        "leave_balance": """
## Your Leave Balance

**Current Balance:**
- **Earned Leave:** {earned_leave} days
- **Sick Leave:** {sick_leave} days  
- **Casual Leave:** {casual_leave} days

**Need Help?** Ask about applications or usage.
""",
        "policy_general": """
## {policy_name}

{policy_content}

**Key Points:** {key_points}

**Questions?** Feel free to ask for clarification.
""",
        "procedure": """
## {procedure_name}

**Steps:** {steps}
**Documents:** {documents}
**Timeline:** {timeline}

**Need Assistance?** Ask me anything about this procedure.
""",
        "text_extraction": """
## Extracted Text

{text_content}

**Note:** Original document text without edits.
""",
        "entity_extraction": """
## Extracted Entities

{entities_list}

**Note:** Named entities found in document.
"""
    }
    return templates.get(response_type, templates["policy_general"])


def create_error_response_template() -> str:
    """
    Creates standardized error response.
    """
    return """
## ❌ Information Not Available

Unable to answer based on current documents.

**Options:**
- Upload relevant document
- Rephrase query  
- Contact HR directly

**I'm here to help** with available documents.
"""


def create_context_prompt() -> str:
    """
    Creates context prompt for document processing.
    """
    return """
Answer using uploaded HR documents by:

1. Using only information in context
2. Applying clear structure  
3. Including exact data (names, dates, penalties)
4. Avoiding assumptions

If answer not present, state clearly and suggest next steps.
"""


def detect_query_type(query: str) -> str:
    """
    Detects query type for appropriate response formatting.
    """
    q = query.lower()
    if "leave" in q and "type" in q:
        return "leave_policy"
    if any(x in q for x in ["balance", "remaining", "days left"]):
        return "leave_balance"  
    if any(x in q for x in ["apply", "how do i", "steps", "process", "procedure"]):
        return "procedure"
    if any(x in q for x in ["extract text", "get full text", "show complete text"]):
        return "text_extraction"
    if any(x in q for x in ["extract entities", "identify names", "show all names", "show all dates"]):
        return "entity_extraction"
    if "policy" in q or "rules" in q:
        return "policy_general"
    return "general"


def detect_response_mode(query: str, intent: str = "unknown", confidence: float = 0.0) -> str:
    """
    Detects appropriate response mode for query.
    """
    q = query.lower()
    if any(x in q for x in ["summary", "summarize", "brief", "short"]):
        return "concise"
    if any(x in q for x in ["explain", "details", "detailed", "comprehensive", "why", "how"]):
        return "detailed"
    if confidence < 0.5:
        return "detailed"
    return "auto"


def should_include_reasoning(query: str, intent: str = "unknown", confidence: float = 0.0) -> bool:
    """
    Determines if reasoning should be included in response.
    """
    q = query.lower()
    if any(x in q for x in ["why", "how", "explain", "reason", "complex", "dispute"]):
        return True
    if confidence < 0.5:
        return True
    if intent in ["procedure", "policy_general"]:
        return True
    return False


def validate_response_format(response: str) -> Dict[str, Any]:
    """
    Validates response format quality.
    """
    checks = {
        "has_headers": "##" in response,
        "has_bullets": "-" in response,
        "has_bold": "**" in response,
        "length_ok": 100 < len(response) < 2000,
        "no_hallucinations": not any(x in response.lower() for x in ["i think", "probably", "maybe"])
    }
    checks["well_structured"] = all(checks.values())
    return checks


def format_leave_types(leave_data: List[Dict[str, Any]]) -> str:
    """
    Formats leave type data for display.
    """
    formatted = []
    for leave in leave_data:
        formatted.append(f"""**{leave.get('type', 'Unknown')}**  
- **Eligibility:** {leave.get('eligibility', 'Not specified')}  
- **Entitlement:** {leave.get('entitlement', 'Not specified')}  
- **Requirements:** {leave.get('requirements', 'None')}""")
    return "\n\n".join(formatted)


# Exported Constants
LEAVE_BALANCE_TEMPLATE = """
## Leave Balance Overview

- **Earned Leave:** {earned_leave} days
- **Sick Leave:** {sick_leave} days
- **Casual Leave:** {casual_leave} days

Need help applying? Just ask!
"""

POLICY_NOT_FOUND_TEMPLATE = """
## Policy Not Found

Requested policy not found in documents.

**Options:**
- Upload different document
- Contact HR
- Ask about available policies
"""

__all__ = [
    'create_hr_assistant_prompt',
    'create_system_prompt', 
    'get_response_mode_instructions',
    'create_formatted_response_template',
    'create_error_response_template',
    'create_context_prompt',
    'detect_query_type',
    'validate_response_format',
    'format_leave_types',
    'LEAVE_BALANCE_TEMPLATE',
    'POLICY_NOT_FOUND_TEMPLATE',
    'detect_response_mode',
    'should_include_reasoning'
]